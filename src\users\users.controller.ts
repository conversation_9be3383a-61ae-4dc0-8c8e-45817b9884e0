import {
  Body,
  Controller,
  Delete,
  Get,
  HttpException,
  Param,
  Patch,
  Post,
  Query,
  UseGuards,
} from '@nestjs/common';
import { UsersService } from './users.service';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { UserResponseDto } from './dto/user-response.dto';
import { ErrorResponseDto } from '../common/dto/error-response.dto';
import { UpdateUserDto } from './dto/updateUser.dto';
import { IQueryResponse, IMeta } from '../interfaces/query-response';
import { IUser } from './interfaces/users.interface';
import { AuthGuard } from 'src/auth/auth.guard';
import { UpdateUserResponseDto } from './dto/update-userResponse.dto';
import { CreateUserDto } from './dto/createUser.dto';

@ApiTags('Users')
@Controller('users')
export class UsersController {
  constructor(private readonly usersService: UsersService) {}

  @Post()
  @ApiOperation({
    summary: 'Create a new user',
    description: 'Register a new user in the system',
  })
  @ApiResponse({
    status: 201,
    description: 'User created successfully',
    type: UserResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request',
    type: ErrorResponseDto,
  })
  async createUser(@Body() createUserDto: CreateUserDto) {
    return this.usersService.createUserFromProfile(createUserDto);
  }

  @Get(':id')
  @ApiOperation({
    summary: 'Get user by ID',
    description: 'Retrieve user information by their unique identifier',
  })
  @ApiResponse({
    status: 200,
    description: 'User found',
    type: UserResponseDto,
  })
  @ApiResponse({
    status: 404,
    description: 'User not found',
    type: ErrorResponseDto,
  })
  async getUserById(@Param('id') id: string) {
    return this.usersService.findUserById(id);
  }

  @Get()
  @ApiOperation({
    summary: 'Get all users',
    description: 'Retrieve information of all users',
  })
  @ApiResponse({
    status: 200,
    description: 'Users found',
    type: [UserResponseDto],
  })
  @ApiResponse({
    status: 500,
    description: 'Internal server error',
    type: ErrorResponseDto,
  })

  @UseGuards(AuthGuard)
  async getAllUsers(@Query('createdBy') createdBy?: string) : Promise<IQueryResponse<Omit<IUser, 'password'>>> {
    const users = await this.usersService.findAllUsers(createdBy);
    const meta: IMeta = {
      totalSize: users.length,
      // chunk: 1,
      // page: 1,
      // limit: users.length,
    };
    return { meta, results: users };
  }

  @UseGuards(AuthGuard)
  @Patch(':id')
  @ApiOperation({
    summary: 'Update user by ID',
    description: 'Update user information by their unique identifier',
  })
  @ApiResponse({
    status: 200,
    description: 'User updated',
    type: UpdateUserResponseDto,
  })
  @ApiResponse({
    status: 404,
    description: 'User not found',
    type: ErrorResponseDto,
  })
  @ApiResponse({
    status: 500,
    description: 'Internal server error',
    type: ErrorResponseDto,
  })

  async updateUser(
    @Param('id') id: string,
    @Body() updateUserDto: UpdateUserDto,
  ) {
    return this.usersService.updateUser(id, updateUserDto);
  }


@UseGuards(AuthGuard)
  @Delete(':id')
  @ApiBearerAuth('Bearer-auth')
  @ApiOperation({
    summary: 'Delete user by ID',
    description: 'Delete user information by their unique identifier',
  })
  @ApiResponse({
    status: 200,
    description: 'User deleted',
    type: UserResponseDto,
  })
  @ApiResponse({
    status: 404,
    description: 'User not found',
    type: ErrorResponseDto,
  })
  @ApiResponse({
    status: 500,
    description: 'Internal server error',
    type: ErrorResponseDto,
  })
  async deleteUser(@Param('id') id: string) {
    const user = await this.usersService.deleteUser(id);
    if (!user) {
      throw new HttpException('User not found', 404);
    }
    return 'User deleted';
  }


}
