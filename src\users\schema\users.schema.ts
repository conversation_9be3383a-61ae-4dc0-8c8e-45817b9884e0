import { Pro<PERSON>, Schem<PERSON>, SchemaFactory } from '@nestjs/mongoose';
import mongoose, { mongo } from 'mongoose';

@Schema({_id:false})
export class GlycemicMonitoringData {
  @Prop({ type: String })
  hba1c?: string;

  @Prop({ type: String })
  fastingGlucose?: string;

  @Prop({ type: String })
  lastTestInterval?: string;

  @Prop({ type: Boolean, default: false })
  hba1cNotRemembered?: boolean;

  @Prop({ type: Boolean, default: false })
  lastTestMoreThanYear?: boolean;
}

@Schema({_id:false})
export class BloodPressureData {
  @Prop({ type: String })
  systolic?: string;

  @Prop({ type: String })
  diastolic?: string;

  @Prop({ type: String })
  pulse?: string;

  @Prop({ type: Boolean, default: false })
  bpNotRemembered?: boolean;
}

@Schema({_id:false})
export class LifeCraftData {
  @Prop({ type: Boolean, default: false })
  analyzing?: boolean;

  @Prop({ type: Boolean, default: false })
  plan?: boolean;
}

@Schema({_id:false})
export class RemindersData {
  @Prop({ type: Boolean, default: false })
  bloodPressure?: boolean;
}

@Schema({_id:false})
export class HealthAssessmentData {
  @Prop({ type: String })
  gender?: string;

  @Prop({ type: String })
  ageRange?: string;

  @Prop({ type: String })
  weight?: string;

  @Prop({ type: String })
  height?: string;

  @Prop({ type: String })
  bmi?: string;

  @Prop({ type: [String] })
  conditionsToManage?: string[];

  @Prop({ type: String })
  diabetesType?: string;

  @Prop({ type: String })
  diabetesDiagnosedSince?: string;

  @Prop({ type: String })
  medicationStatus?: string;

  @Prop({ type: [String] })
  medicationType?: string[];

  @Prop({ type: GlycemicMonitoringData })
  glycemicMonitoring?: GlycemicMonitoringData;

  @Prop({ type: String })
  hypertensionStatus?: string;

  @Prop({ type: String })
  hypertensionSince?: string;

  @Prop({ type: BloodPressureData })
  bloodPressure?: BloodPressureData;

  @Prop({ type: [String] })
  comorbidities?: string[];

  @Prop({ type: String })
  stressLevel?: string;

  @Prop({ type: String })
  emotion?: string;

  @Prop({ type: String })
  sleepQuality?: string;

  @Prop({ type: String })
  fallAsleep?: string;

  @Prop({ type: String })
  fallAsToNightleep?: string;

  @Prop({ type: String })
  diabetesLevel?: string;

  @Prop({ type: String })
  fitnessLevel?: string;

  @Prop({ type: LifeCraftData })
  lifeCraft?: LifeCraftData;

  @Prop({ type: RemindersData })
  reminders?: RemindersData;
}


@Schema({ _id: false }) 
export class MyConditionForm {
  @Prop({ required: true })
  mainCondition: string;

  @Prop()
  diabetesType?: string;

  @Prop()
  hypertensionType?: string;

  @Prop({ type: [String], default: [] })
  otherConditions: string[];
}

@Schema({ _id: false }) 
export class MedsAndSupplementsForm {
  @Prop({ required: true })
  deliveryMethod: string;

  @Prop()
  bolusType?: string;

  @Prop()
  basalType?: string;

  @Prop({ type: [String], default: [] })
  oralMedications: string[];

  @Prop({ type: [String], default: [] })
  supplements: string[];
}
@Schema({ timestamps: true })
export class Users {

  @Prop({type:mongoose.Schema.Types.ObjectId, ref: 'Users',required: false })
  createdBy?: mongoose.Schema.Types.ObjectId;

  @Prop({ required: false })
  firstName?: string;

  @Prop({ required: false })
  lastName?: string;

  @Prop({ required: true })
  email: string;

  @Prop({ required: false })
  password?: string;

  @Prop({ required: false })
  mobileNumber?: string;

  @Prop({ required: false })
  gender?: string;

  @Prop({ required: false })
  dob?: string;

  @Prop({ required: false, default: false })
  isVerified: boolean;

  @Prop({ type: HealthAssessmentData })
  healthAssessmentData?: HealthAssessmentData;

  @Prop({required:false})
  fcmToken?: string;


  @Prop({ required: false ,default:0})
  completedStep?: number;

  @Prop({ required: false })
  photo?: string;

  @Prop({ required: false })
  googleId?: string;

  @Prop({ required: false })
  facebookId?: string;

  @Prop({ required: false })
  provider?: string;

  @Prop({required:false})
  mode?:string;

  @Prop({required:false})
  race?:string;

  @Prop({ type: MyConditionForm, required: false })
  myCondition?: MyConditionForm;

  @Prop({ type: MedsAndSupplementsForm, required: false })
  medsAndSupplements?: MedsAndSupplementsForm;

  @Prop({required:false})
  height?:string;

  @Prop({required:false})
  weight?:string;

  @Prop({required:false})
  photoUrl?:string;
}

export const UsersSchema = SchemaFactory.createForClass(Users);
